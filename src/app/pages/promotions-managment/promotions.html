
<div class="promotions-container">
  <div class="promotions-grid-container">
    <div class="promotions-grid-data-parent-container">
      <div id="foo" class="promotions-grid-data-container">
        <div class="area-filter-container">
          <div class="left-column">
            <div class="main-campaign">
              <div class="panel">
                <div class="panel-body">
                  <div class="wizard">
                    <a class="profile-tab area-tab-width" [ngClass]="{ active: isNewTicket }"
                      (click)="ticketTab('PROMOTIONS')">
                      <span>Promotions<span *ngIf="isNewTicket"> ({{ promotionsCount }})</span></span>
                    </a>
                    <a class="profile-tab area-tab-width" [ngClass]="{ active: isprogressTicket }"
                      (click)="ticketTab('PROGRESS')">
                      <span>Progress<span *ngIf="isprogressTicket"> ({{ progressCount }})</span></span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-column">
            <div class="search-container">
              <div class="input-group">
                <div class="search-input">
                  <span class="input-group-add">
                    <i class="fa fa-search" aria-hidden="true"></i>
                    <input #searchBox class="input-fields" placeholder="Type to search" target [(ngModel)]="model"
                      (ngModelChange)="onSearch($event)" (keypress)="funRestSearchPrevent($event)" />
                    <span (click)="clearSearch()" *ngIf="model !== ''">
                      <img title="Clear" src="../../../assets/img/icons8-cancel-50.png" alt="Example Image" />
                    </span>
                  </span>
                </div>
                <div class="export-button">
                  <button class="add" (click)="onExport($event)" title="Export">
                    <i class="fa fa-share-square-o export-icon"></i>
                  </button>
                </div>
                <div *ngIf="userRole === 'ADMIN' && isNewTicket" class="add-btn-container">
                  <button class="create-level-btn" title="Add" (click)="openAddPromotionDialog()">
                    <img width="18px" src="../../../../assets/img/addButton.png" alt="Add Button" />
                    <span>Add Promotion</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="promotions-table">
          <div class="user-ticket-table">
            <dynamic-table [tableHeads]="tableHead" [tableData]="tableData" [tableConfiguration]="configurationSettings"
              (viewProduct)="openViewProduct($event)" [tableColName]="tableColName"
 (pageChange)="getsupportPageData($event)" [showIndex]="showIndex"
              (emitPromotionEdit)="openDialogForm($event)" (emitPromotionApprove)="approvePromotion($event)" (rejectPromotion)="rejectPromotion($event)"
              (onApproveTarget)="approvePromotion($event)" (onRejectTarget)="rejectPromotion($event)">
            </dynamic-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #viewProductsTemplate>
  <div class="common-popup-container">
    <div class="popup-backdrop" (click)="closePopup()">
      <div class="popup-content" (click)="$event.stopPropagation()">
        <div class="popup-header">
          <h1 class="al-title">Product Details</h1>
        </div>
        <div class="table-container">
          <dynamic-table [tableHeads]="viewProductHead" [tableData]="viewProductData"
            [tableConfiguration]="previewConfigurationSettings" [tableColName]="viewProductColName"
            [showIndex]="showIndex">
          </dynamic-table>
        </div>
        <div class="popup-footer-product">
          <button class="product-cancel-btn" (click)="closePopup()">Close</button>
        </div>
      </div>
    </div>
  </div>
</ng-template>


<!-- Add/Edit Promotions Popup -->
<ng-template #addEditPromotionTemplate>
  <div class="add-promotion-popup">


    <div class="popup-header">
      <h3>{{ isEditMode ? 'Edit Promotion' : 'Add Promotions' }}</h3>
    </div>
    <form [formGroup]="promotionForm" (ngSubmit)="onSubmitPromotion()">
      <div class="popup-content">
        <!-- First Row -->
        <div class="form-row">
          <div class="form-group">
            <label for="promotionName">Promotion name<i class="required">&nbsp;*</i></label>
            <input type="text" id="promotionName" formControlName="promotionName" class="form-input"
                   (input)="triggerFormValidation()" (blur)="triggerFormValidation()"
                   (keypress)="validateTextInput($event)" maxlength="100"
                   placeholder="Enter promotion name">
          </div>
          <div class="form-group">
            <label for="addImage">Upload Image<i class="required">&nbsp;*</i></label>
            <div class="file-upload-wrapper">
              <input type="file" id="addImage" (change)="onFileSelect($event); triggerFormValidation()" accept="image/*" class="file-input"
                [disabled]="isImageUploading">
              <div class="file-upload-display">
                <span class="upload-text" [ngClass]="{'uploading': isImageUploading}">
                  {{ isImageUploading ? 'Uploading...' : (selectedFileName || 'Choose file') }}
                </span>
                <span class="upload-icon">
                  <img width="20px" height="20px" src="../../../assets/img/uploadIcon.png" alt="Upload Icon" />
                </span>
              </div>
            </div>
            <!-- Show success message when image is uploaded -->
          </div>
        </div>

        <!-- Second Row -->
        <div class="form-row">
          <div class="form-group">
            <label for="startDate">Start Date<i class="required">&nbsp;*</i></label>
            <div class="date-picker">
              <input [matDatepicker]="startDatePicker" id="startDate" formControlName="startDate" class="form-input"
                placeholder="Select date" (dateChange)="triggerFormValidation()"
                readonly autocomplete="off" (keydown)="$event.preventDefault()">
              <mat-datepicker-toggle [for]="startDatePicker">
                <i class="fa fa-calendar" matDatepickerToggleIcon></i>
              </mat-datepicker-toggle>
              <mat-datepicker #startDatePicker></mat-datepicker>
            </div>
          </div>
          <div class="form-group">
            <label for="endDate">End Date<i class="required">&nbsp;*</i></label>
            <div class="date-picker">
              <input [matDatepicker]="endDatePicker" id="endDate" formControlName="endDate" class="form-input"
                [min]="promotionForm.get('startDate')?.value" placeholder="Select date" (dateChange)="triggerFormValidation()"
                readonly autocomplete="off" (keydown)="$event.preventDefault()">
              <mat-datepicker-toggle [for]="endDatePicker">
                <i class="fa fa-calendar" matDatepickerToggleIcon></i>
              </mat-datepicker-toggle>
              <mat-datepicker #endDatePicker></mat-datepicker>
            </div>
          </div>
        </div>

        <!-- Third Row -->
        <div class="form-row">
          <div class="form-group">
            <label for="">Region<i class="required">&nbsp;*</i></label>
            <div class="multiselect-wrapper">
              <angular2-multiselect [data]="regionDataList" [(ngModel)]="selectedRegion"
                [ngModelOptions]="{standalone: true}" [settings]="regionDropdownSettings"
                (onSelect)="onRegionSelect($event)" (onDeSelect)="onRegionDeSelect($event)"
                (onSelectAll)="onRegionSelectAll($event)" (onDeSelectAll)="onRegionDeSelectAll($event)">
              </angular2-multiselect>
            </div>
          </div>
          <div class="form-group">
            <label for="zone">Zone<i class="required">&nbsp;*</i></label>
            <div class="multiselect-wrapper">
              <angular2-multiselect [data]="zoneDataList" [(ngModel)]="selectedZone"
                [ngModelOptions]="{standalone: true}" [settings]="zoneDropdownSettings"
                (onSelect)="onZoneSelect($event)" (onDeSelect)="onZoneDeSelect($event)"
                (onSelectAll)="onZoneSelectAll($event)" (onDeSelectAll)="onZoneDeSelectAll($event)"
                (onOpen)="onZoneDropdownOpen()">
              </angular2-multiselect>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="distributorName">Distributor name<i class="required">&nbsp;*</i></label>
            <div class="multiselect-wrapper">
              <angular2-multiselect [data]="distributorDataList" [(ngModel)]="selectedDistributor"
                [ngModelOptions]="{standalone: true}" [settings]="distributorDropdownSettings"
                (onSelect)="onDistributorSelect($event)" (onDeSelect)="onDistributorDeSelect($event)"
                (onSelectAll)="onDistributorSelectAll($event)" (onDeSelectAll)="onDistributorDeSelectAll($event)">
              </angular2-multiselect>
            </div>
          </div>
          <div class="form-group">
            <label for="points">Points Multiplier<i class="required">&nbsp;*</i></label>
            <input type="number" id="points" formControlName="points" class="form-input"
                   (input)="triggerFormValidation()" (blur)="triggerFormValidation()"
                   (keypress)="validateNumberInput($event)" min="1" max="999999"
                   placeholder="Enter points">
          </div>
        </div>

        <div *ngFor="let row of productRows; let i = index" class="form-row product-row" [class.has-delete-button]="canDeleteRow(i)">
          <div class="form-group">
            <label>
              Product Name
              <i class="required">&nbsp;*</i>
            </label>
            <div class="multiselect-wrapper">
              <angular2-multiselect [data]="productDataList" [(ngModel)]="row.selectedProduct"
                [ngModelOptions]="{standalone: true}" [settings]="productDropdownSettings"
                (onSelect)="onAdditionalProductSelect($event, i)" (onDeSelect)="onAdditionalProductDeSelect($event, i)">
              </angular2-multiselect>
            </div>
          </div>

          <div class="form-group">
            <label>
              Target Quantity
              <i class="required">&nbsp;*</i>
            </label>
            <input type="number" [(ngModel)]="row.quantity" [ngModelOptions]="{standalone: true}" class="form-input"
                   (input)="triggerFormValidation()" (blur)="triggerFormValidation()"
                   (keypress)="validateNumberInput($event)" min="1" max="999999"
                   placeholder="Enter target quantity">
          </div>

          <div class="form-group delete-button-group" *ngIf="canDeleteRow(i)">
            <button type="button" class="delete-row-btn" (click)="deleteProductRow(i)"
                    title="Delete this product row">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>

        <div class="add-more-section">
          <button type="button" class="add-more-btn" (click)="addProductRow()" [disabled]="!canAddMore()">
            + Add more
          </button>
        </div>
      </div>

      <div class="popup-footer">
        <button type="button" class="cancel-btn" (click)="closePromotionPopup()">Cancel</button>
        <button type="submit" class="submit-btn"
                [disabled]="!isFormValidSimple()"
                [class.disabled]="!isFormValidSimple()"
                (click)="onSubmitPromotion()">
          Submit
        </button>
      </div>
    </form>
  </div>
</ng-template>

<!-- Approve Promotion Dialog -->
<ng-template #approvePromotionTemplate>
  <div class="confirm-dialog">
    <div class="image-container">
      <img src="../../../assets/img/target-dailog.svg" alt="dialog" />
    </div>
    <div class="confirm-content">
      <span class="text-fields">
        Are you sure you want to approve this promotion?
      </span>
    </div>
    <div class="action-button-section">
      <button class="action-btn submit-btn" (click)="submitApprove()">
        Yes
      </button>
      <button class="action-btn cancel-btn" (click)="cancelApprove()">
        No
      </button>
    </div>
  </div>
</ng-template>

<!--  -->

<!-- Reject Promotion Dialog -->
<ng-template #rejectPromotionTemplate>
  <div class="reject-dialog">
    <h2 class="reject-title">Add Comment</h2>
    <div class="reject-content">
      <textarea [(ngModel)]="rejectComment" placeholder="Enter your comment here..." class="reject-textarea" rows="5">
      </textarea>
    </div>
    <div class="reject-actions">
      <button class="btn-cancel" (click)="cancelReject()">Cancel</button>
      <button class="btn-submit" [disabled]="!rejectComment || rejectComment.length === 0"
        (click)="submitReject()">Submit</button>
    </div>
  </div>
</ng-template>